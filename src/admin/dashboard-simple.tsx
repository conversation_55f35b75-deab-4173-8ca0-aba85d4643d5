import { Box, Text, Input, Button } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';
import React from 'react';
import { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { ThemeProvider } from 'styled-components';
import { theme } from '@adminjs/design-system';

const DashboardContainer = styled(Box)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 32px;
`;

const DashboardHeader = styled(Box)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const SearchContainer = styled(Box)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const UserInfoCard = styled(Box)`
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
`;

const StatsCard = styled(Box)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  }
`;

const SearchResultsContainer = styled(Box)`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
`;

const SearchResultItem = styled(Box)`
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  }
`;

const Dashboard = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userServices, setUserServices] = useState([]);
  const [userInvoices, setUserInvoices] = useState([]);
  const api = new ApiClient();

  const handleSearch = async (value: string) => {
    setSearchTerm(value);
    if (!value.trim()) {
      setSearchResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await api.searchRecords({
        resourceId: 'User',
        query: value,
        searchProperty: 'email',
      });
      setSearchResults(response);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResultClick = (user) => {
    setSelectedUser(user);
    setSearchResults([]);
    setSearchTerm('');
    fetchUserData(user.id);
  };

  const fetchUserData = async (userId) => {
    try {
      const servicesResponse = await api.resourceAction({
        resourceId: 'Service',
        actionName: 'list',
        params: { filters: { userid: userId } },
      });
      setUserServices(servicesResponse.data.records);

      const invoicesResponse = await api.resourceAction({
        resourceId: 'Invoice',
        actionName: 'list',
        params: { filters: { userid: userId } },
      });
      setUserInvoices(invoicesResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch user data:', error);
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <DashboardContainer>
        <DashboardHeader>
          <Box display="flex" alignItems="center" justifyContent="space-between" marginBottom="lg">
            <Box>
              <Text variant="h1" fontWeight="bold" color="primary">
                🚀 Admin Dashboard
              </Text>
              <Text variant="lg" color="grey80" marginTop="sm">
                Manage users, services, and invoices with ease
              </Text>
            </Box>
            <Box display="flex" gap="sm">
              <StatsCard>
                <Text variant="h3" fontWeight="bold">
                  {selectedUser ? '1' : '0'}
                </Text>
                <Text variant="sm">Selected User</Text>
              </StatsCard>
              <StatsCard>
                <Text variant="h3" fontWeight="bold">
                  {userServices.length}
                </Text>
                <Text variant="sm">Services</Text>
              </StatsCard>
              <StatsCard>
                <Text variant="h3" fontWeight="bold">
                  {userInvoices.length}
                </Text>
                <Text variant="sm">Invoices</Text>
              </StatsCard>
            </Box>
          </Box>
        </DashboardHeader>

        <SearchContainer>
          <Text variant="h4" fontWeight="bold" marginBottom="lg" color="primary">
            🔍 User Search
          </Text>
          <Box position="relative" width="100%" maxWidth="600px" marginX="auto">
            <Box display="flex" gap="default">
              <Input
                flex={1}
                placeholder="Search users by email..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                style={{
                  borderRadius: '12px',
                  border: '2px solid #e1e5e9',
                  padding: '12px 16px',
                  fontSize: '16px',
                }}
              />
              <Button
                onClick={() => handleSearch(searchTerm)}
                variant="primary"
                style={{
                  borderRadius: '12px',
                  padding: '12px 24px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  fontWeight: 'bold',
                }}
              >
                Search
              </Button>
            </Box>

            {searchResults.length > 0 && (
              <SearchResultsContainer>
                {searchResults.map((user) => (
                  <SearchResultItem key={user.id} onClick={() => handleResultClick(user)}>
                    <Box display="flex" alignItems="center" gap="sm">
                      <Box
                        width="8px"
                        height="8px"
                        borderRadius="50%"
                        backgroundColor="success"
                      />
                      <Text fontWeight="medium">{user.params.email}</Text>
                      <Text variant="sm" color="grey60">
                        ID: {user.id}
                      </Text>
                    </Box>
                  </SearchResultItem>
                ))}
              </SearchResultsContainer>
            )}

            {isLoading && (
              <Box display="flex" alignItems="center" justifyContent="center" marginTop="lg">
                <Text color="primary">🔄 Searching...</Text>
              </Box>
            )}
          </Box>
        </SearchContainer>

        <Box display="flex" flexDirection="row" gap="lg" marginBottom="xl">
          <Box flex={1}>
            <UserInfoCard>
              <Box display="flex" alignItems="center" gap="sm" marginBottom="lg">
                <Text fontSize="24px">👤</Text>
                <Text variant="h3" fontWeight="bold" color="primary">
                  User Details
                </Text>
              </Box>
              {selectedUser ? (
                <Box>
                  <Box marginBottom="lg">
                    <Box display="grid" gridTemplateColumns="1fr 1fr" gap="md">
                      <Box padding="md" backgroundColor="rgba(102, 126, 234, 0.1)" borderRadius="8px">
                        <Text variant="sm" color="grey60" fontWeight="bold">
                          USER ID
                        </Text>
                        <Text variant="lg" fontWeight="bold" color="primary">
                          #{selectedUser.id}
                        </Text>
                      </Box>
                      <Box padding="md" backgroundColor="rgba(102, 126, 234, 0.1)" borderRadius="8px">
                        <Text variant="sm" color="grey60" fontWeight="bold">
                          EMAIL
                        </Text>
                        <Text fontWeight="medium">{selectedUser.params?.email}</Text>
                      </Box>
                    </Box>
                  </Box>
                  <Box>
                    <Text variant="sm" color="grey60" fontWeight="bold" marginBottom="md">
                      QUICK ACTIONS
                    </Text>
                    <Box display="flex" gap="sm" flexWrap="wrap">
                      <Button
                        onClick={() =>
                          (window.location.href = `/admin/resources/User/records/${selectedUser.id}/show`)
                        }
                        variant="primary"
                        style={{
                          borderRadius: '8px',
                          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          border: 'none',
                          padding: '8px 16px',
                          fontWeight: 'bold',
                        }}
                      >
                        📋 View Details
                      </Button>
                      <Button
                        onClick={() =>
                          window.open(
                            `https://web.flashgates.com/api/v1/users/userToken?token=CCAEStJC4HDt6Hdr58usCV3DMV&email=x&id=${selectedUser.id}`,
                            '_blank'
                          )
                        }
                        variant="secondary"
                        style={{
                          borderRadius: '8px',
                          padding: '8px 16px',
                          fontWeight: 'bold',
                        }}
                      >
                        🚪 Sign In As User
                      </Button>
                    </Box>
                  </Box>
                </Box>
              ) : (
                <Box textAlign="center" padding="xl">
                  <Text fontSize="48px">🔍</Text>
                  <Text variant="lg" color="grey60" marginTop="sm">
                    Search for a user to see details
                  </Text>
                </Box>
              )}
            </UserInfoCard>
          </Box>
        </Box>
      </DashboardContainer>
    </ThemeProvider>
  );
};

export default Dashboard;
