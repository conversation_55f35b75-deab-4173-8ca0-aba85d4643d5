import React from 'react';
import { Box } from '@adminjs/design-system';
import styled from 'styled-components';

const StyledCard = styled(Box)`
  background: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.space.xl};
  border-radius: ${({ theme }) => theme.radii.default};
  box-shadow: ${({ theme }) => theme.shadows.card};
  margin-bottom: ${({ theme }) => theme.space.xl};
  border: 1px solid ${({ theme }) => theme.colors.grey20};

  &:hover {
    box-shadow: ${({ theme }) => theme.shadows.cardHover};
  }
`;

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

const Card: React.FC<CardProps> = ({ children, className }) => {
  return (
    <StyledCard className={className}>
      {children}
    </StyledCard>
  );
};

export default Card;
