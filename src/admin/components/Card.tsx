import React from 'react';
import { Box } from '@adminjs/design-system';
import styled from 'styled-components';

const StyledCard = styled(Box)`
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  margin-bottom: 24px;
  border: 1px solid #f3f4f6;
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    transform: translateY(-1px);
  }
`;

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({ children, className, hover = true }) => {
  return (
    <StyledCard
      className={className}
      style={{
        transition: hover ? 'all 0.2s ease-in-out' : 'none',
      }}
    >
      {children}
    </StyledCard>
  );
};

export default Card;
