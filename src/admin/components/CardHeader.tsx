import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';

const HeaderContainer = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
`;

const HeaderTitle = styled(Text)`
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
`;

const HeaderSubtitle = styled(Text)`
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
`;

interface CardHeaderProps {
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
}

const CardHeader: React.FC<CardHeaderProps> = ({ title, subtitle, children }) => {
  return (
    <HeaderContainer>
      <Box>
        <HeaderTitle>{title}</HeaderTitle>
        {subtitle && <HeaderSubtitle>{subtitle}</HeaderSubtitle>}
      </Box>
      {children && <Box>{children}</Box>}
    </HeaderContainer>
  );
};

export default CardHeader;
