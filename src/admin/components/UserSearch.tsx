import React, { useState, useRef } from 'react';
import { Box, Text, Input, Button } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';
import styled from 'styled-components';
import { User } from '../types/index.js';

const SearchContainer = styled(Box)`
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  margin-bottom: 32px;
  border: 1px solid #f3f4f6;
  position: relative;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const SearchInputContainer = styled(Box)`
  display: flex;
  gap: 12px;
  align-items: center;
`;

const StyledInput = styled.input`
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.2s ease-in-out;
  outline: none;

  &:focus {
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgb(14 165 233 / 0.1);
  }

  &::placeholder {
    color: #9ca3af;
  }
`;

const SearchButton = styled.button`
  padding: 12px 24px;
  background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgb(14 165 233 / 0.4);
  }

  &:active {
    transform: translateY(0);
  }
`;

const SearchResultsContainer = styled(Box)`
  position: absolute;
  top: 100%;
  left: 24px;
  right: 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  margin-top: 8px;
`;

const SearchResultItem = styled(Box)`
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #f8fafc;
    transform: translateX(4px);
  }

  &:first-child {
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  &:last-child {
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }
`;

interface UserSearchProps {
  onUserSelect: (user: User) => void;
}

const UserSearch: React.FC<UserSearchProps> = ({ onUserSelect }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const api = new ApiClient();
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);

  const handleSearch = async (value: string) => {
    setSearchTerm(value);

    // Clear previous timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Don't search if input is empty
    if (!value.trim()) {
      setSearchResults([]);
      return;
    }

    // Debounce search requests
    searchTimeout.current = setTimeout(async () => {
      setIsLoading(true);
      try {
        const response = await api.searchRecords({
          resourceId: 'User',
          query: value,
          searchProperty: 'email',
        });
        setSearchResults(response);
      } catch (error) {
        console.error('Search failed:', error);
      } finally {
        setIsLoading(false);
      }
    }, 300);
  };

  const handleResultClick = (user: User) => {
    onUserSelect(user);
    setSearchResults([]);
    setSearchTerm('');
  };

  const handleConfirmSearch = () => {
    if (searchTerm.trim()) {
      handleSearch(searchTerm);
    }
  };

  return (
    <SearchContainer>
      <SearchInputContainer>
        <StyledInput
          placeholder="Search users by email..."
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          onKeyPress={e => {
            if (e.key === 'Enter') {
              handleConfirmSearch();
            }
          }}
        />
        <SearchButton onClick={handleConfirmSearch}>
          Search
        </SearchButton>
      </SearchInputContainer>

      {searchResults.length > 0 && (
        <SearchResultsContainer>
          {searchResults.map(user => (
            <SearchResultItem key={user.id} onClick={() => handleResultClick(user)}>
              <Text fontWeight="medium" color="#374151">
                {user.params?.email || 'No email'}
              </Text>
            </SearchResultItem>
          ))}
        </SearchResultsContainer>
      )}

      {isLoading && (
        <Box marginTop="md" textAlign="center">
          <Text color="#6b7280">Searching...</Text>
        </Box>
      )}
    </SearchContainer>
  );
};

export default UserSearch;
