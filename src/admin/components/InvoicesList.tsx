import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import Card from './Card.js';
import { Invoice } from '../types/index.js';

interface InvoicesListProps {
  userInvoices: Invoice[];
  isLoadingInvoices: boolean;
  selectedUser: any;
}

const InvoicesList: React.FC<InvoicesListProps> = ({
  userInvoices,
  isLoadingInvoices,
  selectedUser
}) => {
  return (
    <Card>
      <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
        <Text variant="lg" fontWeight="bold">
          Recent Invoices
        </Text>
        {userInvoices.length > 0 && (
          <Text variant="sm" color="grey">
            Showing {Math.min(userInvoices.length, 5)} of {userInvoices.length}
          </Text>
        )}
      </Box>
      {isLoadingInvoices ? (
        <Text>Loading invoices...</Text>
      ) : selectedUser ? (
        userInvoices.length > 0 ? (
          userInvoices.map(invoice => (
            <Box
              key={invoice.id}
              marginBottom="default"
              padding="default"
              backgroundColor="grey20"
              borderRadius="default"
              as="a"
              href={`/admin/resources/Invoice/records/${invoice.id}/show`}
              style={{
                textDecoration: 'none',
                color: 'inherit',
                display: 'block',
                transition: 'transform 0.2s',
                ':hover': {
                  transform: 'translateX(4px)',
                },
              }}
            >
              <Box display="flex" flexDirection="row" justifyContent="space-between" alignItems="center">
                <Box flex={1}>
                  <Box display="flex" alignItems="center" gap="default">
                    <Text fontWeight="bold" color="grey80">
                      #{invoice.id}
                    </Text>
                    <Text variant="lg" fontWeight="bold">
                      ${invoice.params.subtotal}
                    </Text>
                  </Box>
                </Box>

                <Box flex={1} textAlign="right">
                  <Box display="flex" justifyContent="flex-end" alignItems="center" gap="default">
                    <Text color="grey80">Due: {invoice.params.duedate}</Text>
                    <Text
                      backgroundColor={
                        invoice.params.status === 'PAID'
                          ? 'success'
                          : invoice.params.status === 'OVERDUE'
                            ? 'error'
                            : 'warning'
                      }
                      color="white"
                      padding="sm"
                      borderRadius="default"
                      display="inline-block"
                    >
                      {invoice.params.status}
                    </Text>
                  </Box>
                </Box>
              </Box>
            </Box>
          ))
        ) : (
          <Text color="grey">No invoices found</Text>
        )
      ) : (
        <Text color="grey">Select a user to see their invoices</Text>
      )}
    </Card>
  );
};

export default InvoicesList;
