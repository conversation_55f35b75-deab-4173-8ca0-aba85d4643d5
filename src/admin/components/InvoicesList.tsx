import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Invoice } from '../types/index.js';

const InvoiceItem = styled.a`
  display: block;
  padding: 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  margin-bottom: 12px;
  text-decoration: none;
  color: inherit;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 0.1);
    border-color: #9ca3af;
  }
`;

const StatusBadge = styled.span`
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;

  &.paid {
    background: #dcfce7;
    color: #166534;
  }

  &.overdue {
    background: #fef2f2;
    color: #dc2626;
  }

  &.pending {
    background: #fef3c7;
    color: #d97706;
  }
`;

interface InvoicesListProps {
  userInvoices: Invoice[];
  isLoadingInvoices: boolean;
  selectedUser: any;
}

const InvoicesList: React.FC<InvoicesListProps> = ({
  userInvoices,
  isLoadingInvoices,
  selectedUser
}) => {
  return (
    <Card>
      <CardHeader
        title="Recent Invoices"
        subtitle={userInvoices.length > 0 ? `Showing ${Math.min(userInvoices.length, 5)} of ${userInvoices.length}` : undefined}
      />

      {isLoadingInvoices ? (
        <Box textAlign="center" padding="xl">
          <Text color="#6b7280">Loading invoices...</Text>
        </Box>
      ) : selectedUser ? (
        userInvoices.length > 0 ? (
          <Box>
            {userInvoices.map(invoice => (
              <InvoiceItem
                key={invoice.id}
                href={`/admin/resources/Invoice/records/${invoice.id}/show`}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Box display="flex" alignItems="center" gap="md" marginBottom="sm">
                      <Text fontWeight="bold" color="#1f2937">
                        #{invoice.id}
                      </Text>
                      <Text fontSize="lg" fontWeight="bold" color="#0ea5e9">
                        ${invoice.params?.subtotal || '0.00'}
                      </Text>
                    </Box>
                    <Text color="#6b7280" fontSize="sm">
                      Due: {invoice.params?.duedate || 'No due date'}
                    </Text>
                  </Box>

                  <StatusBadge
                    className={
                      invoice.params?.status === 'PAID' ? 'paid' :
                      invoice.params?.status === 'OVERDUE' ? 'overdue' : 'pending'
                    }
                  >
                    {invoice.params?.status || 'PENDING'}
                  </StatusBadge>
                </Box>
              </InvoiceItem>
            ))}
          </Box>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="#9ca3af">No invoices found</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="#9ca3af">Select a user to see their invoices</Text>
        </Box>
      )}
    </Card>
  );
};

export default InvoicesList;
