import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Invoice } from '../types/index.js';

const InvoiceItem = styled.a`
  display: block;
  padding: ${({ theme }) => theme.space.lg};
  background: ${({ theme }) => theme.colors.grey20};
  border: 1px solid ${({ theme }) => theme.colors.grey40};
  border-radius: ${({ theme }) => theme.radii.default};
  margin-bottom: ${({ theme }) => theme.space.default};
  text-decoration: none;
  color: inherit;

  &:hover {
    background: ${({ theme }) => theme.colors.grey40};
  }
`;

const StatusBadge = styled.span`
  padding: ${({ theme }) => theme.space.sm} ${({ theme }) => theme.space.default};
  border-radius: ${({ theme }) => theme.radii.default};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: ${({ theme }) => theme.fontWeights.bold};
  text-transform: uppercase;

  &.paid {
    background: ${({ theme }) => theme.colors.success};
    color: white;
  }

  &.overdue {
    background: ${({ theme }) => theme.colors.error};
    color: white;
  }

  &.pending {
    background: ${({ theme }) => theme.colors.warning};
    color: white;
  }
`;

interface InvoicesListProps {
  userInvoices: Invoice[];
  isLoadingInvoices: boolean;
  selectedUser: any;
}

const InvoicesList: React.FC<InvoicesListProps> = ({
  userInvoices,
  isLoadingInvoices,
  selectedUser
}) => {
  return (
    <Card>
      <CardHeader
        title="Recent Invoices"
        subtitle={userInvoices.length > 0 ? `Showing ${Math.min(userInvoices.length, 5)} of ${userInvoices.length}` : undefined}
      />

      {isLoadingInvoices ? (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Loading invoices...</Text>
        </Box>
      ) : selectedUser ? (
        userInvoices.length > 0 ? (
          <Box>
            {userInvoices.map(invoice => (
              <InvoiceItem
                key={invoice.id}
                href={`/admin/resources/Invoice/records/${invoice.id}/show`}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Box display="flex" alignItems="center" gap="default" marginBottom="sm">
                      <Text fontWeight="bold">
                        #{invoice.id}
                      </Text>
                      <Text variant="lg" fontWeight="bold" color="primary">
                        ${invoice.params?.subtotal || '0.00'}
                      </Text>
                    </Box>
                    <Text color="grey60" variant="sm">
                      Due: {invoice.params?.duedate || 'No due date'}
                    </Text>
                  </Box>

                  <StatusBadge
                    className={
                      invoice.params?.status === 'PAID' ? 'paid' :
                      invoice.params?.status === 'OVERDUE' ? 'overdue' : 'pending'
                    }
                  >
                    {invoice.params?.status || 'PENDING'}
                  </StatusBadge>
                </Box>
              </InvoiceItem>
            ))}
          </Box>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="grey60">No invoices found</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Select a user to see their invoices</Text>
        </Box>
      )}
    </Card>
  );
};

export default InvoicesList;
