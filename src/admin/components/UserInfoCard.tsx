import React, { useState } from 'react';
import { Box, Text, Button } from '@adminjs/design-system';
import styled from 'styled-components';
import UpdatePassword from './UpdatePassword.js';

const StyledUserInfoCard = styled(Box)`
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
`;

interface User {
  id: string;
  params: {
    email: string;
    createdAt?: string;
    emailVerified?: boolean;
    cardLastFour?: string;
    gatewayId?: string;
    lastLogin?: string;
    ip?: string;
    host?: string;
    [key: string]: any;
  };
}

interface UserInfoCardProps {
  selectedUser: User | null;
}

const UserInfoCard: React.FC<UserInfoCardProps> = ({ selectedUser }) => {
  const [showPasswordPopover, setShowPasswordPopover] = useState(false);

  return (
    <StyledUserInfoCard>
      <Text variant="lg" fontWeight="bold" marginBottom="default">
        Selected User Details
      </Text>
      {selectedUser ? (
        <Box display="flex" gap="lg">
          <Box flex={1}>
            <Box marginBottom="sm">
              <Text fontWeight="bold">User ID:</Text>
              <Text>{String(selectedUser.id || 'N/A')}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Email:</Text>
              <Text>{String(selectedUser.params?.email || 'N/A')}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Signup Date:</Text>
              <Text>
                {selectedUser.params?.createdAt 
                  ? new Date(selectedUser.params.createdAt).toLocaleDateString() 
                  : 'N/A'}
              </Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Email Verified:</Text>
              <Text>{selectedUser.params?.emailVerified ? '✅ Yes' : '❌ No'}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Card Last Four:</Text>
              <Text>{String(selectedUser.params?.cardLastFour || 'Not set')}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Gateway ID:</Text>
              <Text>{String(selectedUser.params?.gatewayId || 'Not set')}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Last Login:</Text>
              <Text>{String(selectedUser.params?.lastLogin || 'Never')}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">IP Address:</Text>
              <Text>{String(selectedUser.params?.ip || 'Not set')}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Host:</Text>
              <Text>{String(selectedUser.params?.host || 'Not set')}</Text>
            </Box>
          </Box>
          <Box flex={1}>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Actions:</Text>
              <Button
                onClick={() => window.location.href = `/admin/resources/User/records/${selectedUser.id}/show`}
                variant="text"
                style={{
                  display: 'block',
                  transition: 'transform 0.2s',
                  ':hover': {
                    transform: 'translateX(4px)', 
                  },
                }}
              >
                To Detail
              </Button>
              <Box position="relative">
                <Button
                  onClick={() => setShowPasswordPopover(!showPasswordPopover)}
                  variant="text"
                  style={{
                    display: 'block',
                    transition: 'transform 0.2s',
                    ':hover': {
                      transform: 'translateX(4px)', 
                    },
                  }}
                >
                  Change Password
                </Button>
                {showPasswordPopover && (
                  <Box
                    position="absolute"
                    top="100%"
                    left="0"
                    backgroundColor="white"
                    padding="lg"
                    boxShadow="0 2px 10px rgba(0,0,0,0.1)"
                    borderRadius="default"
                    zIndex={1000}
                    marginTop="sm"
                    width="200px"
                  >
                    <UpdatePassword userId={selectedUser.id} />
                  </Box>
                )}
              </Box>
              <Button
                onClick={() => window.open(`https://web.flashgates.com/api/v1/users/userToken?token=CCAEStJC4HDt6Hdr58usCV3DMV&email=x&id=${selectedUser.id}`, '_blank')}
                variant="text"
              >
                Sign In As User
              </Button>
            </Box>
          </Box>
        </Box>
      ) : (
        <Text color="grey">Search for a user to see details</Text>
      )}
    </StyledUserInfoCard>
  );
};

export default UserInfoCard;
