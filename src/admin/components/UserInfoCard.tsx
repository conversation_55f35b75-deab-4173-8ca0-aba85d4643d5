import React, { useState } from 'react';
import { Box, Text, Button } from '@adminjs/design-system';
import styled from 'styled-components';
import UpdatePassword from './UpdatePassword.js';
import { User } from '../types/index.js';

const StyledUserInfoCard = styled(Box)`
  background: white;
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  margin-bottom: 24px;
  border: 1px solid #f3f4f6;
  transition: all 0.2s ease-in-out;

  &:hover {
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  }
`;

const UserInfoHeader = styled(Box)`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f4f6;
`;

const UserInfoTitle = styled(Text)`
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
`;

const UserInfoGrid = styled(Box)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
`;

const InfoItem = styled(Box)`
  margin-bottom: 16px;
`;

const InfoLabel = styled(Text)`
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const InfoValue = styled(Text)`
  color: #1f2937;
  font-size: 1rem;
  font-weight: 500;
`;

const ActionButton = styled.button`
  display: block;
  width: 100%;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: transparent;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-align: left;

  &:hover {
    border-color: #0ea5e9;
    color: #0ea5e9;
    transform: translateX(4px);
  }
`;

const PrimaryActionButton = styled(ActionButton)`
  background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%);
  color: white;
  border-color: transparent;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(14 165 233 / 0.4);
    color: white;
    border-color: transparent;
  }
`;

interface UserInfoCardProps {
  selectedUser: User | null;
}

const UserInfoCard: React.FC<UserInfoCardProps> = ({ selectedUser }) => {
  const [showPasswordPopover, setShowPasswordPopover] = useState(false);

  return (
    <StyledUserInfoCard>
      <UserInfoHeader>
        <UserInfoTitle>
          Selected User Details
        </UserInfoTitle>
      </UserInfoHeader>

      {selectedUser ? (
        <UserInfoGrid>
          <Box>
            <InfoItem>
              <InfoLabel>User ID</InfoLabel>
              <InfoValue>{String(selectedUser.id || 'N/A')}</InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>Email Address</InfoLabel>
              <InfoValue>{String(selectedUser.params?.email || 'N/A')}</InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>Signup Date</InfoLabel>
              <InfoValue>
                {selectedUser.params?.createdAt
                  ? new Date(selectedUser.params.createdAt).toLocaleDateString()
                  : 'N/A'}
              </InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>Email Verified</InfoLabel>
              <InfoValue>
                <span style={{
                  color: selectedUser.params?.emailVerified ? '#22c55e' : '#ef4444',
                  fontWeight: '600'
                }}>
                  {selectedUser.params?.emailVerified ? '✅ Verified' : '❌ Not Verified'}
                </span>
              </InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>Card Last Four</InfoLabel>
              <InfoValue>{String(selectedUser.params?.cardLastFour || 'Not set')}</InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>Gateway ID</InfoLabel>
              <InfoValue>{String(selectedUser.params?.gatewayId || 'Not set')}</InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>Last Login</InfoLabel>
              <InfoValue>{String(selectedUser.params?.lastLogin || 'Never')}</InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>IP Address</InfoLabel>
              <InfoValue>{String(selectedUser.params?.ip || 'Not set')}</InfoValue>
            </InfoItem>

            <InfoItem>
              <InfoLabel>Host</InfoLabel>
              <InfoValue>{String(selectedUser.params?.host || 'Not set')}</InfoValue>
            </InfoItem>
          </Box>
          <Box>
            <InfoItem>
              <InfoLabel>Quick Actions</InfoLabel>
              <Box marginTop="md">
                <PrimaryActionButton
                  onClick={() => window.location.href = `/admin/resources/User/records/${selectedUser.id}/show`}
                >
                  View Full Details
                </PrimaryActionButton>

                <Box position="relative">
                  <ActionButton
                    onClick={() => setShowPasswordPopover(!showPasswordPopover)}
                  >
                    Change Password
                  </ActionButton>
                  {showPasswordPopover && (
                    <Box
                      position="absolute"
                      top="100%"
                      left="0"
                      backgroundColor="white"
                      padding="lg"
                      boxShadow="0 10px 15px -3px rgb(0 0 0 / 0.1)"
                      borderRadius="lg"
                      zIndex={1000}
                      marginTop="sm"
                      width="280px"
                      border="1px solid #e5e7eb"
                    >
                      <UpdatePassword userId={selectedUser.id} />
                    </Box>
                  )}
                </Box>

                <ActionButton
                  onClick={() => window.open(`https://web.flashgates.com/api/v1/users/userToken?token=CCAEStJC4HDt6Hdr58usCV3DMV&email=x&id=${selectedUser.id}`, '_blank')}
                >
                  Sign In As User
                </ActionButton>
              </Box>
            </InfoItem>
          </Box>
        </UserInfoGrid>
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="#9ca3af" fontSize="lg">
            Search for a user to see their details
          </Text>
        </Box>
      )}
    </StyledUserInfoCard>
  );
};

export default UserInfoCard;
