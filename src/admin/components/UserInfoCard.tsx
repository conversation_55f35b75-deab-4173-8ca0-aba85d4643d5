import React, { useState } from 'react';
import { Box, Text, Button } from '@adminjs/design-system';
import styled from 'styled-components';
import UpdatePassword from './UpdatePassword.js';
import { User } from '../types/index.js';

const StyledUserInfoCard = styled(Box)`
  background: ${({ theme }) => theme.colors.white};
  padding: ${({ theme }) => theme.space.xl};
  border-radius: ${({ theme }) => theme.radii.default};
  box-shadow: ${({ theme }) => theme.shadows.card};
  margin-bottom: ${({ theme }) => theme.space.xl};
  border: 1px solid ${({ theme }) => theme.colors.grey20};
`;

const UserInfoHeader = styled(Box)`
  display: flex;
  align-items: center;
  margin-bottom: ${({ theme }) => theme.space.xl};
  padding-bottom: ${({ theme }) => theme.space.lg};
  border-bottom: 1px solid ${({ theme }) => theme.colors.grey20};
`;

const UserInfoGrid = styled(Box)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.space.xl};

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: ${({ theme }) => theme.space.lg};
  }
`;

const InfoItem = styled(Box)`
  margin-bottom: ${({ theme }) => theme.space.lg};
`;

interface UserInfoCardProps {
  selectedUser: User | null;
}

const UserInfoCard: React.FC<UserInfoCardProps> = ({ selectedUser }) => {
  const [showPasswordPopover, setShowPasswordPopover] = useState(false);

  return (
    <StyledUserInfoCard>
      <UserInfoHeader>
        <Text variant="lg" fontWeight="bold">
          Selected User Details
        </Text>
      </UserInfoHeader>

      {selectedUser ? (
        <UserInfoGrid>
          <Box>
            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">User ID</Text>
              <Text>{String(selectedUser.id || 'N/A')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Email Address</Text>
              <Text>{String(selectedUser.params?.email || 'N/A')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Signup Date</Text>
              <Text>
                {selectedUser.params?.createdAt
                  ? new Date(selectedUser.params.createdAt).toLocaleDateString()
                  : 'N/A'}
              </Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Email Verified</Text>
              <Text color={selectedUser.params?.emailVerified ? 'success' : 'error'}>
                {selectedUser.params?.emailVerified ? '✅ Verified' : '❌ Not Verified'}
              </Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Card Last Four</Text>
              <Text>{String(selectedUser.params?.cardLastFour || 'Not set')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Gateway ID</Text>
              <Text>{String(selectedUser.params?.gatewayId || 'Not set')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Last Login</Text>
              <Text>{String(selectedUser.params?.lastLogin || 'Never')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">IP Address</Text>
              <Text>{String(selectedUser.params?.ip || 'Not set')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Host</Text>
              <Text>{String(selectedUser.params?.host || 'Not set')}</Text>
            </InfoItem>
          </Box>
          <Box>
            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Quick Actions</Text>
              <Box marginTop="default" display="flex" flexDirection="column" gap="sm">
                <Button
                  onClick={() => window.location.href = `/admin/resources/User/records/${selectedUser.id}/show`}
                  variant="primary"
                  size="sm"
                >
                  View Full Details
                </Button>

                <Box position="relative">
                  <Button
                    onClick={() => setShowPasswordPopover(!showPasswordPopover)}
                    variant="text"
                    size="sm"
                  >
                    Change Password
                  </Button>
                  {showPasswordPopover && (
                    <Box
                      position="absolute"
                      top="100%"
                      left="0"
                      backgroundColor="white"
                      padding="lg"
                      boxShadow="cardHover"
                      borderRadius="default"
                      zIndex={1000}
                      marginTop="sm"
                      width="280px"
                      border="1px solid"
                      borderColor="grey20"
                    >
                      <UpdatePassword userId={selectedUser.id} />
                    </Box>
                  )}
                </Box>

                <Button
                  onClick={() => window.open(`https://web.flashgates.com/api/v1/users/userToken?token=CCAEStJC4HDt6Hdr58usCV3DMV&email=x&id=${selectedUser.id}`, '_blank')}
                  variant="text"
                  size="sm"
                >
                  Sign In As User
                </Button>
              </Box>
            </InfoItem>
          </Box>
        </UserInfoGrid>
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">
            Search for a user to see their details
          </Text>
        </Box>
      )}
    </StyledUserInfoCard>
  );
};

export default UserInfoCard;
