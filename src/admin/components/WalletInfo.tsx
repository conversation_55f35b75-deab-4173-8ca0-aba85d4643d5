import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Wallet } from '../types/index.js';

const BalanceCard = styled(Box)`
  background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%);
  padding: 32px;
  border-radius: 16px;
  text-align: center;
  color: white;
  box-shadow: 0 10px 15px -3px rgb(14 165 233 / 0.4);
`;

const WalletGrid = styled(Box)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const InfoItem = styled(Box)`
  padding: 16px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
`;

const InfoLabel = styled(Text)`
  font-weight: 600;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;

const InfoValue = styled(Text)`
  color: #1f2937;
  font-size: 1rem;
  font-weight: 500;
`;

interface WalletInfoProps {
  userWallet: Wallet | null;
  isLoadingWallet: boolean;
  selectedUser: any;
}

const WalletInfo: React.FC<WalletInfoProps> = ({
  userWallet,
  isLoadingWallet,
  selectedUser
}) => {
  return (
    <Card>
      <CardHeader title="Wallet Information" />

      {isLoadingWallet ? (
        <Box textAlign="center" padding="xl">
          <Text color="#6b7280">Loading wallet information...</Text>
        </Box>
      ) : selectedUser ? (
        userWallet ? (
          <WalletGrid>
            <BalanceCard>
              <Text fontSize="3xl" fontWeight="bold" marginBottom="sm">
                ${userWallet.params?.balance || '0.00'}
              </Text>
              <Text fontSize="lg" style={{ opacity: 0.9 }}>
                Current Balance
              </Text>
            </BalanceCard>

            <Box display="flex" flexDirection="column" gap="md">
              <InfoItem>
                <InfoLabel>Wallet ID</InfoLabel>
                <InfoValue>{userWallet.id}</InfoValue>
              </InfoItem>

              <InfoItem>
                <InfoLabel>Status</InfoLabel>
                <InfoValue>
                  <span style={{
                    color: userWallet.params?.status === 'ACTIVE' ? '#22c55e' : '#ef4444',
                    fontWeight: '600'
                  }}>
                    {userWallet.params?.status || 'Unknown'}
                  </span>
                </InfoValue>
              </InfoItem>

              <InfoItem>
                <InfoLabel>Last Updated</InfoLabel>
                <InfoValue>
                  {userWallet.params?.updatedAt
                    ? new Date(userWallet.params.updatedAt).toLocaleString()
                    : 'Never'
                  }
                </InfoValue>
              </InfoItem>
            </Box>
          </WalletGrid>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="#9ca3af">No wallet found for this user</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="#9ca3af">Select a user to see wallet details</Text>
        </Box>
      )}
    </Card>
  );
};

export default WalletInfo;
