import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import Card from './Card.js';

interface Wallet {
  id: string;
  params: {
    balance: string;
    status: string;
    updatedAt: string;
    [key: string]: any;
  };
}

interface WalletInfoProps {
  userWallet: Wallet | null;
  isLoadingWallet: boolean;
  selectedUser: any;
}

const WalletInfo: React.FC<WalletInfoProps> = ({
  userWallet,
  isLoadingWallet,
  selectedUser
}) => {
  return (
    <Card>
      <Text variant="lg" fontWeight="bold" marginBottom="default">
        Wallet Balance
      </Text>
      {isLoadingWallet ? (
        <Text>Loading wallet information...</Text>
      ) : selectedUser ? (
        userWallet ? (
          <Box display="flex" flexDirection="row" gap="xl">
            <Box flex={1} padding="xl" backgroundColor="primary100" borderRadius="lg">
              <Text variant="h2" textAlign="center">
                ${userWallet.params.balance}
              </Text>
              <Text textAlign="center" color="grey100">
                Current Balance
              </Text>
            </Box>

            <Box flex={1} display="flex" flexDirection="column" justifyContent="center">
              <Box marginBottom="lg">
                <Text fontWeight="bold">Wallet ID:</Text>
                <Text>{userWallet.id}</Text>
              </Box>
              <Box marginBottom="lg">
                <Text fontWeight="bold">Status:</Text>
                <Text>{userWallet.params.status}</Text>
              </Box>
              <Box>
                <Text fontWeight="bold">Last Updated:</Text>
                <Text>{new Date(userWallet.params.updatedAt).toLocaleString()}</Text>
              </Box>
            </Box>
          </Box>
        ) : (
          <Text color="grey">No wallet found for this user</Text>
        )
      ) : (
        <Text color="grey">Select a user to see wallet details</Text>
      )}
    </Card>
  );
};

export default WalletInfo;
