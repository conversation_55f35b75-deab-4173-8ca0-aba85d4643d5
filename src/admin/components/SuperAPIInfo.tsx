import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import Card from './Card.js';
import SuperAPI from './SuperAPI.js';

interface Service {
  id: string;
  [key: string]: any;
}

interface SuperAPIInfoProps {
  currentService: Service | null;
  superApiKey: number;
}

const SuperAPIInfo: React.FC<SuperAPIInfoProps> = ({ currentService, superApiKey }) => {
  return (
    <Card>
      <Text variant="lg" fontWeight="bold" marginBottom="default">
        SuperAPI Info
      </Text>
      {currentService ? (
        <SuperAPI key={superApiKey} serviceId={currentService.id} />
      ) : (
        <Text color="grey">Select a service to view SuperAPI info</Text>
      )}
    </Card>
  );
};

export default SuperAPIInfo;
