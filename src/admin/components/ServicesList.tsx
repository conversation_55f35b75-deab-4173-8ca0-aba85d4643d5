import React from 'react';
import { Box, Text, Button } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Service } from '../types/index.js';

const ServiceItem = styled(Box)`
  padding: ${({ theme }) => theme.space.lg};
  background: ${props => props.isSelected ? props.theme.colors.primary20 : props.theme.colors.grey20};
  border: 1px solid ${props => props.isSelected ? props.theme.colors.primary : props.theme.colors.grey40};
  border-radius: ${({ theme }) => theme.radii.default};
  cursor: pointer;
  margin-bottom: ${({ theme }) => theme.space.default};

  &:hover {
    background: ${props => props.isSelected ? props.theme.colors.primary20 : props.theme.colors.grey40};
  }
`;

interface ServicesListProps {
  userServices: Service[];
  isLoadingServices: boolean;
  selectedUser: any;
  currentService: Service | null;
  onServiceSelect: (service: Service) => void;
}

const ServicesList: React.FC<ServicesListProps> = ({
  userServices,
  isLoadingServices,
  selectedUser,
  currentService,
  onServiceSelect
}) => {
  return (
    <Card>
      <CardHeader
        title="Services List"
        subtitle={userServices.length > 0 ? `Total: ${userServices.length}` : undefined}
      />

      {isLoadingServices ? (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Loading services...</Text>
        </Box>
      ) : selectedUser ? (
        userServices.length > 0 ? (
          <Box>
            {userServices.map(service => (
              <ServiceItem
                key={service.id}
                isSelected={currentService?.id === service.id}
                onClick={() => onServiceSelect(service)}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text fontWeight="bold">#{service.id}</Text>
                    <Text color="grey60">{service.params?.name || 'Unnamed Service'}</Text>
                  </Box>
                  <Button
                    variant={currentService?.id === service.id ? "primary" : "text"}
                    size="sm"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      onServiceSelect(service);
                    }}
                  >
                    {currentService?.id === service.id ? 'Selected' : 'Select'}
                  </Button>
                </Box>
              </ServiceItem>
            ))}
          </Box>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="grey60">No services found</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">Select a user to see their services</Text>
        </Box>
      )}
    </Card>
  );
};

export default ServicesList;
