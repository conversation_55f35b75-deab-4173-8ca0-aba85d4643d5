import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import styled from 'styled-components';
import Card from './Card.js';
import CardHeader from './CardHeader.js';
import { Service } from '../types/index.js';

const ServiceItem = styled(Box)`
  padding: 16px;
  background: ${props => props.isSelected ? 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)' : '#f9fafb'};
  border: 2px solid ${props => props.isSelected ? '#0ea5e9' : '#e5e7eb'};
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  margin-bottom: 12px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 0.1);
    border-color: ${props => props.isSelected ? '#0ea5e9' : '#9ca3af'};
  }
`;

const ServiceButton = styled.button`
  padding: 8px 16px;
  background: ${props => props.isSelected ? 'linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%)' : 'transparent'};
  color: ${props => props.isSelected ? 'white' : '#374151'};
  border: 2px solid ${props => props.isSelected ? 'transparent' : '#e5e7eb'};
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-1px);
    box-shadow: ${props => props.isSelected ? '0 4px 12px rgb(14 165 233 / 0.4)' : '0 2px 4px rgb(0 0 0 / 0.1)'};
  }
`;

interface ServicesListProps {
  userServices: Service[];
  isLoadingServices: boolean;
  selectedUser: any;
  currentService: Service | null;
  onServiceSelect: (service: Service) => void;
}

const ServicesList: React.FC<ServicesListProps> = ({
  userServices,
  isLoadingServices,
  selectedUser,
  currentService,
  onServiceSelect
}) => {
  return (
    <Card>
      <CardHeader
        title="Services List"
        subtitle={userServices.length > 0 ? `Total: ${userServices.length}` : undefined}
      />

      {isLoadingServices ? (
        <Box textAlign="center" padding="xl">
          <Text color="#6b7280">Loading services...</Text>
        </Box>
      ) : selectedUser ? (
        userServices.length > 0 ? (
          <Box>
            {userServices.map(service => (
              <ServiceItem
                key={service.id}
                isSelected={currentService?.id === service.id}
                onClick={() => onServiceSelect(service)}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text fontWeight="bold" color="#1f2937">#{service.id}</Text>
                    <Text color="#6b7280">{service.params?.name || 'Unnamed Service'}</Text>
                  </Box>
                  <ServiceButton
                    isSelected={currentService?.id === service.id}
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      onServiceSelect(service);
                    }}
                  >
                    {currentService?.id === service.id ? 'Selected' : 'Select'}
                  </ServiceButton>
                </Box>
              </ServiceItem>
            ))}
          </Box>
        ) : (
          <Box textAlign="center" padding="xl">
            <Text color="#9ca3af">No services found</Text>
          </Box>
        )
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="#9ca3af">Select a user to see their services</Text>
        </Box>
      )}
    </Card>
  );
};

export default ServicesList;
