import React from 'react';
import { Box, Text, Button } from '@adminjs/design-system';
import Card from './Card.js';

interface Service {
  id: string;
  params: {
    name: string;
    [key: string]: any;
  };
}

interface ServicesListProps {
  userServices: Service[];
  isLoadingServices: boolean;
  selectedUser: any;
  currentService: Service | null;
  onServiceSelect: (service: Service) => void;
}

const ServicesList: React.FC<ServicesListProps> = ({
  userServices,
  isLoadingServices,
  selectedUser,
  currentService,
  onServiceSelect
}) => {
  return (
    <Card>
      <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
        <Text variant="lg" fontWeight="bold">
          Services List
        </Text>
        {userServices.length > 0 && (
          <Text variant="sm" color="grey">
            Total: {userServices.length}
          </Text>
        )}
      </Box>
      {isLoadingServices ? (
        <Text>Loading services...</Text>
      ) : selectedUser ? (
        userServices.length > 0 ? (
          <Box display="flex" flexDirection="column" gap="sm">
            {userServices.map(service => (
              <Box
                key={service.id}
                padding="lg"
                backgroundColor={currentService?.id === service.id ? 'primary100' : 'grey20'}
                borderRadius="default"
                onClick={() => onServiceSelect(service)}
                style={{ cursor: 'pointer' }}
              >
                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Text fontWeight="bold">#{service.id}</Text>
                    <Text>{service.params.name}</Text>
                  </Box>
                  <Button
                    variant={currentService?.id === service.id ? "primary" : "text"}
                    size="sm"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      onServiceSelect(service);
                    }}
                  >
                    {currentService?.id === service.id ? 'Selected' : 'Select'}
                  </Button>
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Text color="grey">No services found</Text>
        )
      ) : (
        <Text color="grey">Select a user to see their services</Text>
      )}
    </Card>
  );
};

export default ServicesList;
