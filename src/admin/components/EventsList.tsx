import React from 'react';
import { Box, Text } from '@adminjs/design-system';
import Card from './Card.js';

interface Event {
  id: string;
  params: {
    createdAt: string;
    type: string;
    context: string;
    [key: string]: any;
  };
}

interface EventsListProps {
  userEvents: Event[];
  isLoadingEvents: boolean;
  selectedUser: any;
}

const EventsList: React.FC<EventsListProps> = ({
  userEvents,
  isLoadingEvents,
  selectedUser
}) => {
  return (
    <Card>
      <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
        <Text variant="lg" fontWeight="bold">
          Recent Events
        </Text>
        {userEvents.length > 0 && (
          <Text variant="sm" color="grey">
            Last {userEvents.length} events
          </Text>
        )}
      </Box>

      {isLoadingEvents ? (
        <Text>Loading events...</Text>
      ) : selectedUser ? (
        userEvents.length > 0 ? (
          <Box>
            {userEvents.map((event, index) => (
              <Box
                key={event.id}
                marginBottom="default"
                padding="lg"
                backgroundColor={index % 2 === 0 ? 'grey20' : 'white'}
                borderRadius="default"
                display="flex"
                flexDirection="row"
                gap="xl"
                alignItems="center"
              >
                {/* Time Column */}
                <Box flex={0.2}>
                  <Text color="grey80" variant="sm">
                    {new Date(event.params.createdAt).toLocaleTimeString()}
                  </Text>
                  <Text color="grey80" variant="sm">
                    {new Date(event.params.createdAt).toLocaleDateString()}
                  </Text>
                </Box>

                {/* Event Type Badge */}
                <Box
                  backgroundColor={
                    event.params.type === 'ERROR'
                      ? 'error'
                      : event.params.type === 'WARNING'
                        ? 'warning'
                        : 'success'
                  }
                  padding="sm"
                  borderRadius="default"
                  flex={0.15}
                >
                  <Text color="white" textAlign="center" variant="sm">
                    {event.params.type}
                  </Text>
                </Box>

                {/* Event Message */}
                <Box flex={0.65}>
                  <Text variant="sm" color="grey100">
                    {event.params.context}
                  </Text>
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Text color="grey">No events found for this user</Text>
        )
      ) : (
        <Text color="grey">Select a user to see their events</Text>
      )}
    </Card>
  );
};

export default EventsList;
