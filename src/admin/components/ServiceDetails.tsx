import React, { useState } from 'react';
import { Box, Text, Button, Input } from '@adminjs/design-system';
import Card from './Card.js';
import { Service, Product, Pricing } from '../types/index.js';

interface ServiceDetailsProps {
  currentService: Service | null;
  products: Product[];
  pricings: Pricing[];
  isLoadingProducts: boolean;
  onServiceUpdate: (updatedService: Service) => void;
  isUpdatingService: boolean;
}

const ServiceDetails: React.FC<ServiceDetailsProps> = ({
  currentService,
  products,
  pricings,
  isLoadingProducts,
  onServiceUpdate,
  isUpdatingService
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedService, setEditedService] = useState<Service | null>(null);

  const handleEdit = () => {
    if (currentService) {
      setEditedService({ ...currentService });
      setIsEditing(true);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditedService(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editedService) {
      onServiceUpdate(editedService);
      setIsEditing(false);
    }
  };

  return (
    <Card>
      <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="default">
        <Text variant="lg" fontWeight="bold">
          Service Details
        </Text>
        {currentService && !isEditing && (
          <Button
            onClick={handleEdit}
            variant="primary"
            size="sm"
          >
            Edit
          </Button>
        )}
      </Box>
      {currentService ? (
        isEditing && editedService ? (
          <Box as="form" onSubmit={handleSubmit} display="flex" flexDirection="column" gap="sm">
            <Box marginBottom="sm">
              <Text fontWeight="bold">Service ID:</Text>
              <Text>#{editedService.id}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Status:</Text>
              <select
                value={editedService.params.domainstatus}
                onChange={e => setEditedService({
                  ...editedService,
                  params: { ...editedService.params, domainstatus: e.target.value }
                })}
                style={{ width: '100%', padding: '8px', borderRadius: '4px' }}
              >
                <option value="ACTIVE">ACTIVE</option>
                <option value="SUSPENDED">SUSPENDED</option>
                <option value="CANCELLED">CANCELLED</option>
              </select>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Package:</Text>
              {isLoadingProducts ? (
                <Text>Loading packages...</Text>
              ) : (
                <select
                  value={editedService.params.packageid || ''}
                  onChange={e => setEditedService({
                    ...editedService,
                    params: { ...editedService.params, packageid: e.target.value }
                  })}
                  style={{ width: '100%', padding: '8px', borderRadius: '4px' }}
                >
                  <option value="">Select Package</option>
                  {products.map(product => (
                    <option key={product.id} value={product.id}>
                      {product.params.name} - {
                        pricings.find(p => p.params.relid === product.id)?.params[currentService.params.billingcycle] || 'No price'
                      }
                    </option>
                  ))}
                </select>
              )}
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Next Due Date:</Text>
              <Input
                type="date"
                value={editedService.params.nextduedate ? 
                  new Date(editedService.params.nextduedate).toISOString().split('T')[0] : 
                  ''
                }
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEditedService({
                  ...editedService,
                  params: { ...editedService.params, nextduedate: e.target.value }
                })}
              />
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Billing Cycle:</Text>
              <select
                value={editedService.params.billingcycle || 'MONTHLY'}
                onChange={e => setEditedService({
                  ...editedService,
                  params: { ...editedService.params, billingcycle: e.target.value }
                })}
                style={{ width: '100%', padding: '8px', borderRadius: '4px' }}
              >
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="semiannually">Semi-Annually</option>
                <option value="annually">Annually</option>
                <option value="biennially">Biennially</option>
              </select>
            </Box>
            <Box display="flex" gap="default" marginTop="lg">
              <Button
                type="submit"
                variant="primary"
                disabled={isUpdatingService}
              >
                {isUpdatingService ? 'Saving...' : 'Save Changes'}
              </Button>
              <Button
                onClick={handleCancel}
                variant="text"
                disabled={isUpdatingService}
              >
                Cancel
              </Button>
            </Box>
          </Box>
        ) : (
          <Box display="flex" flexDirection="column" gap="sm">
            <Box marginBottom="sm">
              <Text fontWeight="bold">Service ID:</Text>
              <Text>#{currentService.id}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Status:</Text>
              <Text>{currentService.params.domainstatus}</Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Package:</Text>
              <Text>
                {products.find(p => p.id === currentService.params.packageid)?.params.name || 
                 currentService.params.packageid || 
                 'Not set'}
              </Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Next Due Date:</Text>
              <Text>
                {currentService.params.nextduedate 
                  ? currentService.params.nextduedate
                  : 'Not set'}
              </Text>
            </Box>
            <Box marginBottom="sm">
              <Text fontWeight="bold">Billing Cycle:</Text>
              <Text>{currentService.params.billingcycle || 'Not set'}</Text>
            </Box>
          </Box>
        )
      ) : (
        <Text color="grey">Select a service to view details</Text>
      )}
    </Card>
  );
};

export default ServiceDetails;
