/* Responsive styles for Dashboard */

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Reduce padding on mobile */
  .dashboard-container {
    padding: 16px !important;
  }
  
  /* Stack cards vertically on mobile */
  .grid-container {
    grid-template-columns: 1fr !important;
    gap: 16px !important;
  }
  
  /* Adjust card padding on mobile */
  .card {
    padding: 16px !important;
  }
  
  /* Make search container full width on mobile */
  .search-container {
    margin-left: 0 !important;
    margin-right: 0 !important;
    max-width: none !important;
  }
  
  /* Adjust font sizes for mobile */
  .dashboard-title {
    font-size: 1.5rem !important;
  }
  
  .card-title {
    font-size: 1.125rem !important;
  }
  
  /* Hide less important information on mobile */
  .mobile-hidden {
    display: none !important;
  }
  
  /* Make buttons full width on mobile */
  .mobile-full-width {
    width: 100% !important;
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .three-column-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .dashboard-container {
    padding: 24px !important;
  }
}

/* Large screen optimizations */
@media (min-width: 1440px) {
  .dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .card {
    background: #1f2937 !important;
    border-color: #374151 !important;
  }
  
  .dashboard-container {
    background: linear-gradient(135deg, #111827 0%, #1f2937 100%) !important;
  }
}

/* Print styles */
@media print {
  .dashboard-container {
    background: white !important;
    padding: 0 !important;
  }
  
  .card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
    break-inside: avoid;
  }
  
  .no-print {
    display: none !important;
  }
}
