import { Box, Text, Input, Button } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';
import React from 'react';
import { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { ThemeProvider } from 'styled-components';
import { theme } from '@adminjs/design-system';
import SuperAPI from './components/SuperAPI.js';
import UpdatePassword from './components/UpdatePassword.js';

const SearchResultsContainer = styled(Box)`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.5;
  z-index: 1000;
  backdrop-filter: blur(10px);
`;

const SearchResultItem = styled(Box)`
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 4px 8px;

  &:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: translateX(4px);
  }

  &:first-child {
    margin-top: 8px;
  }

  &:last-child {
    margin-bottom: 8px;
  }
`;

const UserInfoCard = styled(Box)`
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  }
`;

const DashboardContainer = styled(Box)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  padding: 32px;
`;

const DashboardHeader = styled(Box)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const SearchContainer = styled(Box)`
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const StatsCard = styled(Box)`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
  }
`;

const Dashboard = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userServices, setUserServices] = useState([]);
  const [userInvoices, setUserInvoices] = useState([]);
  const [isLoadingServices, setIsLoadingServices] = useState(false);
  const [isLoadingInvoices, setIsLoadingInvoices] = useState(false);
  const [userWallet, setUserWallet] = useState(null);
  const [isLoadingWallet, setIsLoadingWallet] = useState(false);
  const [userEvents, setUserEvents] = useState([]);
  const [isLoadingEvents, setIsLoadingEvents] = useState(false);
  const [currentService, setCurrentService] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editedService, setEditedService] = useState(null);
  const [isUpdatingService, setIsUpdatingService] = useState(false);
  const [products, setProducts] = useState([]);
  const [pricings, setPricings] = useState([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [userEmails, setUserEmails] = useState([]);
  const [isLoadingEmails, setIsLoadingEmails] = useState(false);
  const api = new ApiClient();
  const searchTimeout = useRef(null);
  const [superApiKey, setSuperApiKey] = useState(0);

  const handleSearch = async (value: string) => {
    setSearchTerm(value);

    // Clear previous timeout
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Don't search if input is empty
    if (!value.trim()) {
      setSearchResults([]);
      return;
    }

    // Debounce search requests
    searchTimeout.current = setTimeout(async () => {
      setIsLoading(true);
      try {
        const response = await api.searchRecords({
          resourceId: 'User',
          query: value,
          searchProperty: 'email',
        });
        setSearchResults(response);
      } catch (error) {
        console.error('Search failed:', error);
      } finally {
        setIsLoading(false);
      }
    }, 300);
  };

  const fetchUserData = async (userId, email )=> {
    // Fetch services
    setIsLoadingServices(true);
    try {
      const servicesResponse = await api.resourceAction({
        resourceId: 'Service',
        actionName: 'list',
        params: {
          filters: {
            userid: userId,
          },
        },
      });
      setUserServices(servicesResponse.data.records);
      if (servicesResponse.data.records.length === 1) {
        setCurrentService(servicesResponse.data.records[0]);
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);
    } finally {
      setIsLoadingServices(false);
    }

    // Fetch invoices
    setIsLoadingInvoices(true);
    try {
      const invoicesResponse = await api.resourceAction({
        resourceId: 'Invoice',
        actionName: 'list',
        params: {
          filters: {
            userid: userId,
          },
          sortBy: 'duedate',
          direction: 'desc',
          page: 1,
          perPage: 5,
        },
      });
      setUserInvoices(invoicesResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch invoices:', error);
    } finally {
      setIsLoadingInvoices(false);
    }

    // Add wallet fetching
    setIsLoadingWallet(true);
    try {
      const walletResponse = await api.resourceAction({
        resourceId: 'Wallet',
        actionName: 'list',
        params: {
          filters: {
            userId: userId,
          },
        },
      });
      setUserWallet(walletResponse.data.records[0]); // Assuming one wallet per user
    } catch (error) {
      console.error('Failed to fetch wallet:', error);
    } finally {
      setIsLoadingWallet(false);
    }

    // Add events fetching
    setIsLoadingEvents(true);
    try {
      const eventsResponse = await api.resourceAction({
        resourceId: 'Event',
        actionName: 'list',
        params: {
          filters: {
            userId: userId,
          },
          page: 1,
          perPage: 5,
          sortBy: 'createdAt',
          direction: 'desc',
        },
      });
      setUserEvents(eventsResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch events:', error);
    } finally {
      setIsLoadingEvents(false);
    }

    // Add notify emails fetching
    setIsLoadingEmails(true);
    try {
      const emailsResponse = await api.resourceAction({
        resourceId: 'NotifyEmail',
        actionName: 'list',
        params: {
          filters: {
            receivers: email,
          },
          sortBy: 'createdAt',
          direction: 'desc',
          page: 1,
          perPage: 5,
        },
      });
      setUserEmails(emailsResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch notify emails:', error);
    } finally {
      setIsLoadingEmails(false);
    }
  };

  const handleResultClick = user => {
    setSelectedUser(user);
    setSearchResults([]);
    setSearchTerm('');
    fetchUserData(user.id, user.params.email);
  };

  const handleConfirmSearch = () => {
    if (searchTerm.trim()) {
      handleSearch(searchTerm);
    }
  };

  const handleServiceUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdatingService(true);
    
    // Define the fields that are allowed to be edited
    const editableFields = ['domainstatus', 'packageid', 'nextduedate', 'billingcycle'];
    
    // Create an object with only the editable fields
    const updateData = editableFields.reduce((acc, field) => {
      if (editedService.params[field] !== currentService.params[field]) {
        acc[field] = editedService.params[field];
      }
      return acc;
    }, {});

    // Only proceed if there are actual changes
    if (Object.keys(updateData).length === 0) {
      setIsEditing(false);
      setIsUpdatingService(false);
      return;
    }
    
    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: editedService.id,
        actionName: 'edit',
        data: updateData,
      });
      setCurrentService(response.data.record);
      setIsEditing(false);
      setSuperApiKey(prev => prev + 1);
    } catch (error) {
      console.error('Failed to update service:', error);
    } finally {
      setIsUpdatingService(false);
    }
  };

  const fetchProductsAndPricing = async () => {
    setIsLoadingProducts(true);
    try {
      // Fetch products
      const productsResponse = await api.resourceAction({
        resourceId: 'Product',
        actionName: 'list',
      });
      setProducts(productsResponse.data.records);

      // Fetch pricing
      const pricingResponse = await api.resourceAction({
        resourceId: 'Pricing',
        actionName: 'list',
      });
      setPricings(pricingResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch products and pricing:', error);
    } finally {
      setIsLoadingProducts(false);
    }
  };

  useEffect(() => {
    fetchProductsAndPricing();
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <DashboardContainer>
        <DashboardHeader>
          <Box display="flex" alignItems="center" justifyContent="space-between" marginBottom="lg">
            <Box>
              <Text variant="h1" fontWeight="bold" color="primary">
                🚀 Admin Dashboard
              </Text>
              <Text variant="lg" color="grey80" marginTop="sm">
                Manage users, services, and invoices with ease
              </Text>
            </Box>
            <Box display="flex" gap="sm">
              <StatsCard>
                <Text variant="h3" fontWeight="bold">
                  {selectedUser ? '1' : '0'}
                </Text>
                <Text variant="sm">Selected User</Text>
              </StatsCard>
              <StatsCard>
                <Text variant="h3" fontWeight="bold">
                  {userServices.length}
                </Text>
                <Text variant="sm">Services</Text>
              </StatsCard>
              <StatsCard>
                <Text variant="h3" fontWeight="bold">
                  {userInvoices.length}
                </Text>
                <Text variant="sm">Invoices</Text>
              </StatsCard>
            </Box>
          </Box>
        </DashboardHeader>

        <SearchContainer>
          <Text variant="h4" fontWeight="bold" marginBottom="lg" color="primary">
            🔍 User Search
          </Text>
          <Box position="relative" width="100%" maxWidth="600px" marginX="auto">
            <Box display="flex" gap="default">
              <Input
                flex={1}
                placeholder="Search users by email..."
                value={searchTerm}
                onChange={e => setSearchTerm(e.target.value)}
                onKeyPress={e => {
                  if (e.key === 'Enter') {
                    handleConfirmSearch();
                  }
                }}
                style={{
                  borderRadius: '12px',
                  border: '2px solid #e1e5e9',
                  padding: '12px 16px',
                  fontSize: '16px',
                  transition: 'all 0.3s ease'
                }}
              />
              <Button
                onClick={handleConfirmSearch}
                variant="primary"
                style={{
                  borderRadius: '12px',
                  padding: '12px 24px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  fontWeight: 'bold'
                }}
              >
                Search
              </Button>
            </Box>

            {searchResults.length > 0 && (
              <SearchResultsContainer>
                {searchResults.map(user => (
                  <SearchResultItem key={user.id} onClick={() => handleResultClick(user)}>
                    <Box display="flex" alignItems="center" gap="sm">
                      <Box
                        width="8px"
                        height="8px"
                        borderRadius="50%"
                        backgroundColor="success"
                      />
                      <Text fontWeight="medium">{user.params.email}</Text>
                      <Text variant="sm" color="grey60">ID: {user.id}</Text>
                    </Box>
                  </SearchResultItem>
                ))}
              </SearchResultsContainer>
            )}

            {isLoading && (
              <Box display="flex" alignItems="center" justifyContent="center" marginTop="lg">
                <Text color="primary">🔄 Searching...</Text>
              </Box>
            )}
          </Box>
        </SearchContainer>

        {/* User Details Row */}
        <Box display="flex" flexDirection="row" gap="lg" marginBottom="xl">
          {/* User Details Column */}
          <Box flex={1}>
            <UserInfoCard>
              <Box display="flex" alignItems="center" gap="sm" marginBottom="lg">
                <Text fontSize="24px">👤</Text>
                <Text variant="h3" fontWeight="bold" color="primary">
                  User Details
                </Text>
              </Box>
              {selectedUser ? (
                <Box display="grid" gridTemplateColumns="1fr 1fr" gap="lg">
                  <Box>
                    <Box
                      marginBottom="md"
                      padding="md"
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="8px"
                    >
                      <Text variant="sm" color="grey60" fontWeight="bold">USER ID</Text>
                      <Text variant="lg" fontWeight="bold" color="primary">
                        #{String(selectedUser.id || 'N/A')}
                      </Text>
                    </Box>
                    <Box
                      marginBottom="md"
                      padding="md"
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="8px"
                    >
                      <Text variant="sm" color="grey60" fontWeight="bold">EMAIL</Text>
                      <Text fontWeight="medium">{String(selectedUser.params?.email || 'N/A')}</Text>
                    </Box>
                    <Box
                      marginBottom="md"
                      padding="md"
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="8px"
                    >
                      <Text variant="sm" color="grey60" fontWeight="bold">SIGNUP DATE</Text>
                      <Text>{selectedUser.params?.createdAt ? new Date(selectedUser.params.createdAt).toLocaleDateString() : 'N/A'}</Text>
                    </Box>
                    <Box
                      marginBottom="md"
                      padding="md"
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="8px"
                    >
                      <Text variant="sm" color="grey60" fontWeight="bold">EMAIL VERIFIED</Text>
                      <Text>{selectedUser.params?.emailVerified ? '✅ Verified' : '❌ Not Verified'}</Text>
                    </Box>
                  </Box>
                  <Box>
                    <Box
                      marginBottom="md"
                      padding="md"
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="8px"
                    >
                      <Text variant="sm" color="grey60" fontWeight="bold">PAYMENT INFO</Text>
                      <Text>Card: {String(selectedUser.params?.cardLastFour || 'Not set')}</Text>
                      <Text variant="sm">Gateway: {String(selectedUser.params?.gatewayId || 'Not set')}</Text>
                    </Box>
                    <Box
                      marginBottom="md"
                      padding="md"
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="8px"
                    >
                      <Text variant="sm" color="grey60" fontWeight="bold">LAST LOGIN</Text>
                      <Text>{String(selectedUser.params?.lastLogin || 'Never')}</Text>
                    </Box>
                    <Box
                      marginBottom="md"
                      padding="md"
                      backgroundColor="rgba(102, 126, 234, 0.1)"
                      borderRadius="8px"
                    >
                      <Text variant="sm" color="grey60" fontWeight="bold">CONNECTION INFO</Text>
                      <Text variant="sm">IP: {String(selectedUser.params?.ip || 'Not set')}</Text>
                      <Text variant="sm">Host: {String(selectedUser.params?.host || 'Not set')}</Text>
                    </Box>
                  </Box>
                </Box>
                {/* Action Buttons */}
                <Box marginTop="lg">
                  <Text variant="sm" color="grey60" fontWeight="bold" marginBottom="md">QUICK ACTIONS</Text>
                  <Box display="flex" gap="sm" flexWrap="wrap">
                    <Button
                      onClick={() => window.location.href = `/admin/resources/User/records/${selectedUser.id}/show`}
                      variant="primary"
                      style={{
                        borderRadius: '8px',
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        border: 'none',
                        padding: '8px 16px',
                        fontWeight: 'bold',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      📋 View Details
                    </Button>
                    <Box position="relative">
                      <Button
                        onClick={e => {
                          e.preventDefault();
                          e.stopPropagation();
                          const popover = e.currentTarget.nextElementSibling;
                          if (popover) {
                            popover.style.display = popover.style.display === 'none' ? 'block' : 'none';
                          }
                        }}
                        variant="secondary"
                        style={{
                          borderRadius: '8px',
                          padding: '8px 16px',
                          fontWeight: 'bold',
                          transition: 'all 0.3s ease'
                        }}
                      >
                        🔑 Change Password
                      </Button>
                      <Box
                        position="absolute"
                        top="100%"
                        left="0"
                        backgroundColor="white"
                        padding="lg"
                        boxShadow="0 8px 32px rgba(0,0,0,0.15)"
                        borderRadius="12px"
                        zIndex={1000}
                        display="none"
                        marginTop="sm"
                        width="250px"
                        border="1px solid rgba(255, 255, 255, 0.2)"
                      >
                        <UpdatePassword userId={selectedUser.id} />
                      </Box>
                    </Box>
                    <Button
                      onClick={() => window.open(`https://web.flashgates.com/api/v1/users/userToken?token=CCAEStJC4HDt6Hdr58usCV3DMV&email=x&id=${selectedUser.id}`, '_blank')}
                      variant="secondary"
                      style={{
                        borderRadius: '8px',
                        padding: '8px 16px',
                        fontWeight: 'bold',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      🚪 Sign In As User
                    </Button>
                  </Box>
                </Box>
              ) : (
                <Box textAlign="center" padding="xl">
                  <Text fontSize="48px">🔍</Text>
                  <Text variant="lg" color="grey60" marginTop="sm">
                    Search for a user to see details
                  </Text>
                </Box>
              )}
            </UserInfoCard>
          </Box>
        </Box>

        {/* Services Row - Three Columns */}
        <Box display="flex" flexDirection="row" gap="lg" marginBottom="xl">
          {/* Column 1: Services List */}
          <Box flex={1}>
            <UserInfoCard>
              <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
                <Box display="flex" alignItems="center" gap="sm">
                  <Text fontSize="20px">⚙️</Text>
                  <Text variant="h4" fontWeight="bold" color="primary">
                    Services
                  </Text>
                </Box>
                {userServices.length > 0 && (
                  <StatsCard style={{ padding: '8px 16px', fontSize: '14px' }}>
                    <Text variant="sm" fontWeight="bold">
                      {userServices.length} Total
                    </Text>
                  </StatsCard>
                )}
              </Box>
              {isLoadingServices ? (
                <Text>Loading services...</Text>
              ) : selectedUser ? (
                userServices.length > 0 ? (
                  <Box display="flex" flexDirection="column" gap="sm">
                    {userServices.map(service => (
                      <Box
                        key={service.id}
                        padding="lg"
                        backgroundColor={currentService?.id === service.id ? 'primary100' : 'grey20'}
                        borderRadius="default"
                        onClick={() => setCurrentService(service)}
                        style={{ cursor: 'pointer' }}
                      >
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Box>
                            <Text fontWeight="bold">#{service.id}</Text>
                            <Text>{service.params.name}</Text>
                          </Box>
                          <Button
                            variant={currentService?.id === service.id ? "primary" : "text"}
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCurrentService(service);
                            }}
                          >
                            {currentService?.id === service.id ? 'Selected' : 'Select'}
                          </Button>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Text color="grey">No services found</Text>
                )
              ) : (
                <Text color="grey">Select a user to see their services</Text>
              )}
            </UserInfoCard>
          </Box>

          {/* Column 2: Current Service Details */}
          <Box flex={1}>
            <UserInfoCard>
              <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="default">
                <Text variant="lg" fontWeight="bold">
                  Service Details
                </Text>
                {currentService && !isEditing && (
                  <Button
                    onClick={() => {
                      setEditedService({...currentService});
                      setIsEditing(true);
                    }}
                    variant="primary"
                    size="sm"
                  >
                    Edit
                  </Button>
                )}
              </Box>
              {currentService ? (
                isEditing ? (
                  <Box as="form" onSubmit={handleServiceUpdate} display="flex" flexDirection="column" gap="sm">
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Service ID:</Text>
                      <Text>#{editedService.id}</Text>
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Status:</Text>
                      <select
                        value={editedService.params.domainstatus}
                        onChange={e => setEditedService({
                          ...editedService,
                          params: { ...editedService.params, domainstatus: e.target.value }
                        })}
                        style={{ width: '100%', padding: '8px', borderRadius: '4px' }}
                      >
                        <option value="ACTIVE">ACTIVE</option>
                        <option value="SUSPENDED">SUSPENDED</option>
                        <option value="CANCELLED">CANCELLED</option>
                      </select>
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Package:</Text>
                      {isLoadingProducts ? (
                        <Text>Loading packages...</Text>
                      ) : (
                        <select
                          value={editedService.params.packageid || ''}
                          onChange={e => setEditedService({
                            ...editedService,
                            params: { ...editedService.params, packageid: e.target.value }
                          })}
                          style={{ width: '100%', padding: '8px', borderRadius: '4px' }}
                        >
                          <option value="">Select Package</option>
                          {products.map(product => (
                            <option key={product.id} value={product.id}>
                              {product.params.name} - {
                                pricings.find(p => p.params.relid === product.id)?.params[currentService.params.billingcycle] || 'No price'
                              }
                            </option>
                          ))}
                        </select>
                      )}
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Next Due Date:</Text>
                      <Input
                        type="date"
                        value={editedService.params.nextduedate ? 
                          new Date(editedService.params.nextduedate).toISOString().split('T')[0] : 
                          ''
                        }
                        onChange={e => setEditedService({
                          ...editedService,
                          params: { ...editedService.params, nextduedate: e.target.value }
                        })}
                      />
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Billing Cycle:</Text>
                      <select
                        value={editedService.params.billingcycle || 'MONTHLY'}
                        onChange={e => setEditedService({
                          ...editedService,
                          params: { ...editedService.params, billingcycle: e.target.value }
                        })}
                        style={{ width: '100%', padding: '8px', borderRadius: '4px' }}
                      >
                        <option value="monthly">Monthly</option>
                        <option value="quarterly">Quarterly</option>
                        <option value="semiannually">Semi-Annually</option>
                        <option value="annually">Annually</option>
                        <option value="biennially">Biennially</option>
                      </select>
                    </Box>
                    <Box display="flex" gap="default" marginTop="lg">
                      <Button
                        type="submit"
                        variant="primary"
                        disabled={isUpdatingService}
                      >
                        {isUpdatingService ? 'Saving...' : 'Save Changes'}
                      </Button>
                      <Button
                        onClick={() => {
                          setIsEditing(false);
                          setEditedService(null);
                        }}
                        variant="text"
                        disabled={isUpdatingService}
                      >
                        Cancel
                      </Button>
                    </Box>
                  </Box>
                ) : (
                  <Box display="flex" flexDirection="column" gap="sm">
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Service ID:</Text>
                      <Text>#{currentService.id}</Text>
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Status:</Text>
                      <Text>{currentService.params.domainstatus}</Text>
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Package:</Text>
                      <Text>
                        {products.find(p => p.id === currentService.params.packageid)?.params.name || 
                         currentService.params.packageid || 
                         'Not set'}
                      </Text>
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Next Due Date:</Text>
                      <Text>
                        {currentService.params.nextduedate 
                          ? currentService.params.nextduedate
                          : 'Not set'}
                      </Text>
                    </Box>
                    <Box marginBottom="sm">
                      <Text fontWeight="bold">Billing Cycle:</Text>
                      <Text>{currentService.params.billingcycle || 'Not set'}</Text>
                    </Box>
                  </Box>
                )
              ) : (
                <Text color="grey">Select a service to view details</Text>
              )}
            </UserInfoCard>
          </Box>

          {/* Column 3: SuperAPI Info */}
          <Box flex={1}>
            <UserInfoCard>
              <Text variant="lg" fontWeight="bold" marginBottom="default">
                SuperAPI Info
              </Text>
              {currentService ? (
                <SuperAPI key={superApiKey} serviceId={currentService.id} />
              ) : (
                <Text color="grey">Select a service to view SuperAPI info</Text>
              )}
            </UserInfoCard>
          </Box>
        </Box>

        {/* Invoices Row */}
        <Box display="flex" flexDirection="row" gap="lg" marginBottom="xl">

          {/* Invoices Column */}
          <Box flex={1}>
            <UserInfoCard>
              <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
                <Text variant="lg" fontWeight="bold">
                  Recent Invoices
                </Text>
                {userInvoices.length > 0 && (
                  <Text variant="sm" color="grey">
                    Showing {Math.min(userInvoices.length, 5)} of {userInvoices.length}
                  </Text>
                )}
              </Box>
              {isLoadingInvoices ? (
                <Text>Loading invoices...</Text>
              ) : selectedUser ? (
                userInvoices.length > 0 ? (
                  userInvoices.map(invoice => (
                    <Box
                      key={invoice.id}
                      marginBottom="default"
                      padding="default"
                      backgroundColor="grey20"
                      borderRadius="default"
                      as="a"
                      href={`/admin/resources/Invoice/records/${invoice.id}/show`}
                      style={{
                        textDecoration: 'none',
                        color: 'inherit',
                        display: 'block',
                        transition: 'transform 0.2s',
                        ':hover': {
                          transform: 'translateX(4px)',
                        },
                      }}
                    >
                      <Box display="flex" flexDirection="row" justifyContent="space-between" alignItems="center">
                        <Box flex={1}>
                          <Box display="flex" alignItems="center" gap="default">
                            <Text fontWeight="bold" color="grey80">
                              #{invoice.id}
                            </Text>
                            <Text variant="lg" fontWeight="bold">
                              ${invoice.params.subtotal}
                            </Text>
                          </Box>
                        </Box>

                        <Box flex={1} textAlign="right">
                          <Box display="flex" justifyContent="flex-end" alignItems="center" gap="default">
                            <Text color="grey80">Due: {invoice.params.duedate}</Text>
                            <Text
                              backgroundColor={
                                invoice.params.status === 'PAID'
                                  ? 'success'
                                  : invoice.params.status === 'OVERDUE'
                                    ? 'error'
                                    : 'warning'
                              }
                              color="white"
                              padding="sm"
                              borderRadius="default"
                              display="inline-block"
                            >
                              {invoice.params.status}
                            </Text>
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  ))
                ) : (
                  <Text color="grey">No invoices found</Text>
                )
              ) : (
                <Text color="grey">Select a user to see their invoices</Text>
              )}
            </UserInfoCard>
          </Box>
          {/* Events Column */}
          <Box flex={1}>
            <UserInfoCard>
              <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
                <Text variant="lg" fontWeight="bold">
                  Recent Events
                </Text>
                {userEvents.length > 0 && (
                  <Text variant="sm" color="grey">
                    Last {userEvents.length} events
                  </Text>
                )}
              </Box>

              {isLoadingEvents ? (
                <Text>Loading events...</Text>
              ) : selectedUser ? (
                userEvents.length > 0 ? (
                  <Box>
                    {userEvents.map((event, index) => (
                      <Box
                        key={event.id}
                        marginBottom="default"
                        padding="lg"
                        backgroundColor={index % 2 === 0 ? 'grey20' : 'white'}
                        borderRadius="default"
                        display="flex"
                        flexDirection="row"
                        gap="xl"
                        alignItems="center"
                      >
                        {/* Time Column */}
                        <Box flex={0.2}>
                          <Text color="grey80" variant="sm">
                            {new Date(event.params.createdAt).toLocaleTimeString()}
                          </Text>
                          <Text color="grey80" variant="sm">
                            {new Date(event.params.createdAt).toLocaleDateString()}
                          </Text>
                        </Box>

                        {/* Event Type Badge */}
                        <Box
                          backgroundColor={
                            event.params.type === 'ERROR'
                              ? 'error'
                              : event.params.type === 'WARNING'
                                ? 'warning'
                                : 'success'
                          }
                          padding="sm"
                          borderRadius="default"
                          flex={0.15}
                        >
                          <Text color="white" textAlign="center" variant="sm">
                            {event.params.type}
                          </Text>
                        </Box>

                        {/* Event Message */}
                        <Box flex={0.65}>
                          <Text variant="sm" color="grey100">
                            {event.params.context}
                          </Text>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Text color="grey">No events found for this user</Text>
                )
              ) : (
                <Text color="grey">Select a user to see their events</Text>
              )}
            </UserInfoCard>
          </Box>
        </Box>
        {/* Notify Emails Row */}
        <Box display="flex" flexDirection="row" marginBottom="xl">
          <Box flex={1}>
            <UserInfoCard>
              <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom="lg">
                <Text variant="lg" fontWeight="bold">
                  Recent Notify Emails
                </Text>
                {userEmails.length > 0 && (
                  <Text variant="sm" color="grey">
                    Last {userEmails.length} emails
                  </Text>
                )}
              </Box>

              {isLoadingEmails ? (
                <Text>Loading emails...</Text>
              ) : selectedUser ? (
                userEmails.length > 0 ? (
                  <Box>
                    {userEmails.map((email, index) => (
                      <Box
                        key={email.id}
                        marginBottom="default"
                        padding="lg"
                        backgroundColor={index % 2 === 0 ? 'grey20' : 'white'}
                        borderRadius="default"
                      >
                        <Box display="flex" justifyContent="space-between" marginBottom="sm">
                          <Text fontWeight="bold">
                            {email.params.subject}
                          </Text>
                          <Text color="grey80">
                          {new Date(email.params.createdAt).toLocaleString()}
                          </Text>
                        </Box>
                        
                        <Box 
                          backgroundColor="white" 
                          padding="lg" 
                          borderRadius="default"
                          marginTop="sm"
                          style={{ 
                            maxHeight: '100px', 
                            overflow: 'auto',
                            border: '1px solid #eee' 
                          }}
                        >
                          <Text variant="sm" style={{ whiteSpace: 'pre-wrap' }}>
                            {email.params.templateParam}
                          </Text>
                        </Box>

                        <Box display="flex" gap="default" marginTop="sm">
                          {email.params.error && (
                            <Text variant="sm" color="error">
                              Error: {email.params.error}
                            </Text>
                          )}
                        </Box>
                      </Box>
                    ))}
                  </Box>
                ) : (
                  <Text color="grey">No notify emails found for this user</Text>
                )
              ) : (
                <Text color="grey">Select a user to see their notify emails</Text>
              )}
            </UserInfoCard>
          </Box>
        </Box>
        {/* Wallet Row */}
        <Box display="flex" flexDirection="row" marginBottom="xl">
          <Box flex={1}>
            <UserInfoCard>
              <Text variant="lg" fontWeight="bold" marginBottom="default">
                Wallet Balance
              </Text>
              {isLoadingWallet ? (
                <Text>Loading wallet information...</Text>
              ) : selectedUser ? (
                userWallet ? (
                  <Box display="flex" flexDirection="row" gap="xl">
                    <Box flex={1} padding="xl" backgroundColor="primary100" borderRadius="lg">
                      <Text variant="h2" textAlign="center">
                        ${userWallet.params.balance}
                      </Text>
                      <Text textAlign="center" color="grey100">
                        Current Balance
                      </Text>
                    </Box>

                    <Box flex={1} display="flex" flexDirection="column" justifyContent="center">
                      <Box marginBottom="lg">
                        <Text fontWeight="bold">Wallet ID:</Text>
                        <Text>{userWallet.id}</Text>
                      </Box>
                      <Box marginBottom="lg">
                        <Text fontWeight="bold">Status:</Text>
                        <Text>{userWallet.params.status}</Text>
                      </Box>
                      <Box>
                        <Text fontWeight="bold">Last Updated:</Text>
                        <Text>{new Date(userWallet.params.updatedAt).toLocaleString()}</Text>
                      </Box>
                    </Box>
                  </Box>
                ) : (
                  <Text color="grey">No wallet found for this user</Text>
                )
              ) : (
                <Text color="grey">Select a user to see wallet details</Text>
              )}
            </UserInfoCard>
          </Box>
        </Box>
      </DashboardContainer>
    </ThemeProvider>
  );
};

export default Dashboard;
